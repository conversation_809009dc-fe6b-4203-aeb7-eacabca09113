import {ToolInterface} from './base.js';
import {
    <PERSON><PERSON>,
    BotTokenUsage
} from '../models/associations.js';
import {log} from '../library/log.js';
import {l, lp} from "../library/translation.js";
import {sequelize} from "../db.js";
import {Op} from "sequelize";
import {
    getTokenBalance, getTokenUsageByBot,
    mlmApiCall,
    recordTokenUsage,
    tokenUsageByBots,
    totalTokenUsage,
    getPaymentLink
} from "../utils/index.js";
import dotenv from "dotenv";

dotenv.config();
class TokenTrackingTool extends ToolInterface {
    getDefinition() {
        // This tool doesn't expose any OpenAI function
        return {};
    }

    async execute(args) {
        // This tool is only used for event handling
        return false;
    }

    getRegisteredEvents() {
        return [
            'answer_received',      // For tracking token usage from bot responses
            'message_received',     // For checking if user has enough tokens before processing a message
            'menu_callback',        // For handling menu callbacks
            'callback_query'        // For handling button callbacks
        ];
    }

    getMenuItems() {
        const menu = [
            {
                id: 'token_balance',
                text: '💰 Tokens and Subscription',
                callback_data: 'token_balance',
                position: 'main',
                permissions: ['owner'],
                order: 50
            },
            {
                id: 'token_statistics',
                text: '📊 Token Statistics',
                callback_data: 'token_statistics',
                position: 'token_menu',
                permissions: ['owner'],
                order: 20
            },
            {
                id: 'token_menu_back',
                text: '⬅️ Back to Main Menu',
                callback_data: 'token_menu_back',
                position: 'token_menu',
                permissions: ['owner'],
                order: 100
            },
            {
                id: 'token_buy',
                text: '💳 Buy More Tokens',
                callback_data: 'token_buy',
                position: 'token_menu',
                permissions: ['owner'],
                order: 15
            },
            {
                id: 'token_usage_all',
                text: '📈 All-Time Usage',
                callback_data: 'token_usage_all',
                position: 'token_statistics_menu',
                permissions: ['owner'],
                order: 10
            },
            {
                id: 'token_usage_month',
                text: '📅 This Month',
                callback_data: 'token_usage_month',
                position: 'token_statistics_menu',
                permissions: ['owner'],
                order: 20
            },
            {
                id: 'token_usage_week',
                text: '📆 Last 7 Days',
                callback_data: 'token_usage_week',
                position: 'token_statistics_menu',
                permissions: ['owner'],
                order: 30
            },
            {
                id: 'token_statistics_back',
                text: '⬅️ Back to Token Menu',
                callback_data: 'token_balance',
                position: 'token_statistics_menu',
                permissions: ['owner'],
                order: 100
            },

        ];
        if (process.env.PAYMENTS_TIMBI && process.env.PAYMENTS_TIMBI === 'true') {
            menu.push({
                id: 'token_buy_timbi',
                text: '💎 Pay with Timbi',
                callback_data: 'pay_provider_timbi',
                position: 'token_buy_menu',
                permissions: ['owner'],
                order: 10
            });
        }
        if (process.env.PAYMENTS_ODB && process.env.PAYMENTS_ODB === 'true') {
            menu.push({
                id: 'token_buy_odb',
                text: '💳 Pay with ODB',
                callback_data: 'pay_provider_odb',
                position: 'token_buy_menu',
                permissions: ['owner'],
                order: 20
            });
        }
        if (process.env.PAYMENTS_ONE && process.env.PAYMENTS_ONE === 'true') {
            menu.push({
                id: 'token_buy_one',
                text: '💰 Pay with One',
                callback_data: 'pay_provider_one',
                position: 'token_buy_menu',
                permissions: ['owner'],
                order: 30
            });
        }
        if (process.env.PAYMENTS_STARS && process.env.PAYMENTS_STARS === 'true') {
            menu.push({
                id: 'token_buy_stars',
                text: '⭐ Pay with Stars',
                callback_data: 'pay_provider_stars',
                position: 'token_buy_menu',
                permissions: ['owner'],
                order: 40
            });
        }
        menu.push({
            id: 'token_buy_back',
            text: '⬅️ Back to Token Menu',
            callback_data: 'token_balance',
            position: 'token_buy_menu',
            permissions: ['owner'],
            order: 100
        });
        return menu;
    }

    async handleEvent(eventName, eventData, context) {
        try {
            switch (eventName) {
                case 'answer_received':
                    // non-blocking track token usage from bot responses
                    this.trackTokenUsage(eventData, context).then().catch((e) => {
                        log.error('Error tracking token usage', {
                            error: e.message,
                            eventName: eventName
                        }, 'handleEvent', 'TokenTrackingTool');
                    });
                    return false;

                case 'message_received':
                    // Check if user has enough tokens before processing a message
                    return await this.checkTokenBalance(context);

                case 'menu_callback':
                    if (!context.isBotOwner) {
                        return false;
                    }
                    // Handle menu callbacks
                    const {menuItem} = eventData;
                    const callbackData = menuItem.callback_data;

                    switch (callbackData) {
                        case 'token_balance':
                            return await this.showTokenBalanceMenu(context);

                        case 'token_statistics':
                            return await this.showTokenStatisticsMenu(context);

                        case 'token_usage_all':
                            return await this.showTokenUsageAll(context);

                        case 'token_usage_month':
                            const firstDay = new Date();
                            firstDay.setDate(1);
                            firstDay.setHours(0, 0, 0, 0);
                            return await this.showTokenUsageAll(context, firstDay);

                        case 'token_usage_week':
                            const sevenDaysAgo = new Date();
                            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                            sevenDaysAgo.setHours(0, 0, 0, 0);
                            return await this.showTokenUsageAll(context, sevenDaysAgo);

                        case 'token_menu_back':
                            await context.client.showMainMenu(context.chatId, context.user);
                            return true;
                        case 'token_buy':
                            return await this.showTokenBuyMenu(context);
                        case 'pay_provider_timbi':
                        case 'pay_provider_odb':
                        case 'pay_provider_one':
                        case 'pay_provider_stars':
                            return await this.handlePaymentProvider(callbackData.split('_')[2], context);
                        default:
                            return false;
                    }

                case 'callback_query':
                    if (!context.isBotOwner) {
                        return false;
                    }
                    // Handle button callbacks for dynamic items (bot details)
                    return await this.handleCallbackQuery(eventData, context);
            }
            return false;
        } catch (error) {
            log.error('Token tracking error', {
                error: error.message,
                eventName: eventName
            }, 'handleEvent', 'TokenTrackingTool');
            return false;
        }
    }

    /**
     * Show menu with available payment providers for buying tokens
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if menu was shown
     */
    async showTokenBuyMenu(context) {
        const {client, chatId, user} = context;

        try {
            // Get menu items for token_buy_menu position
            const menuItems = await client.getMenuItems('token_buy_menu', ['owner']);

            // Check if user has community access for discount
            const mlmId = user.externalId;
            const paidUser = await mlmApiCall('/user/paid/' + mlmId, 'GET', {});

            // Prepare message with pricing info
            const message = "💳 " + await l(user, "<b>Buy More Tokens</b>") + "\n\n" +
                await l(user, "Select payment method:") + "\n\n" +
                (paidUser ?
                        await l(user, "🎉 Community member discount: 50% OFF!") :
                        await l(user, "💡 Join our community to get 50% discount!")
                );

            // Send menu to user
            await client.sendMenu(chatId, message, menuItems);
            return true;
        } catch (error) {
            log.error('Error showing token buy menu', {
                error: error.message,
                chatId
            }, 'showTokenBuyMenu', 'TokenTrackingTool');

            await client.sendMessage(
                chatId,
                await l(user, '<b>Error:</b> Unable to show payment options. Please try again later.'),
                {parse_mode: 'HTML'}
            );
            return true;
        }
    }

    /**
     * Handle payment provider selection and generate payment link
     * @param {string} provider - Selected payment provider (timbi, odb, one, stars)
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if handled successfully
     */
    async handlePaymentProvider(provider, context) {
        const {client, chatId, user, bot} = context;
        let logMeta = {
            provider: provider,
            chatId: chatId,
            botId: bot.id
        };
        log.info('Handling payment provider', logMeta, 'handlePaymentProvider', 'TokenTrackingTool');
        try {
            // Get MLM user ID
            const mlmUserId = user.externalId;

            // Check if provider is enabled in settings
            if (process.env[`PAYMENTS_${provider.toUpperCase()}`] !== 'true') {
                await client.sendMessage(chatId, await l(user, 'Payment provider is not enabled'));
                return true;
            }

            // Get payment link
            const link = await getPaymentLink(mlmUserId, provider, 'tokens', 4);
            if (!link) {
                await client.sendMessage(chatId, await l(user, 'Error getting payment link'));
                return true;
            }

            let response;
            // Handle Stars payment differently
            if (provider === 'stars') {
                response = await client.sendInvoice(
                    chatId,
                    'DXRT.ai Services Fee',
                    'DXRT.ai Services Fee ' + link.price + ' EUR value',
                    JSON.stringify(link),
                    '',
                    'XTR',
                    [{amount: link.stars, label: "fee"}],
                    {}
                );
            } else {
                // Send payment link to user
                const message = "💳 " + await l(user, "<b>Payment Link Generated</b>") + "\n\n" +
                    await l(user, "Click the button below to proceed with payment:");

                response = await client.sendMessage(chatId, message, {
                    parse_mode: 'HTML',
                    reply_markup: {
                        inline_keyboard: [[
                            {
                                text: await l(user, "🔗 Pay Now"),
                                url: link.link
                            }
                        ]]
                    }
                });
            }

            setTimeout(async () => {
                if (!response) return;
                await client.deleteMessage(chatId, response);
            }, 60000);

            return true;
        } catch (error) {
            log.error('Error handling payment provider', {
                error: error.message,
                provider,
                chatId,
                botId: bot.id
            }, 'handlePaymentProvider', 'TokenTrackingTool');

            await client.sendMessage(
                chatId,
                await l(user, '<b>Error:</b> Unable to generate payment link. Please try again later.'),
                {parse_mode: 'HTML'}
            );
            return true;
        }
    }

    /**
     * Track token usage from bot responses
     * @param {Object} eventData - Contains the answer object with token usage info
     * @param {Object} context - Context object with bot and user info
     * @returns {Promise<boolean>} - Returns true if event should stop propagation
     */
    async trackTokenUsage(eventData, context) {
        const {answer} = eventData;
        const {bot, user} = context;

        // Skip if no token count information
        if (!answer || !answer.aiTokenCount || !answer.aiTokenCount.total_tokens) {
            return false;
        }

        try {
            // Get the bot to find its owner

            if (!bot || !bot.user_id) {
                log.warn('Bot or bot owner not found', {botId: bot.id}, 'trackTokenUsage', 'TokenTrackingTool');
                return false;
            }

            const modelTokensUsed = answer.aiTokenCount.total_tokens || 0;
            const model = answer.aiTokenCount.model || 'unknown';

            const newBalance = await recordTokenUsage(bot.user_id, modelTokensUsed, model, bot.id, answer.id);

            //@todo notifications

            log.info('Token usage tracked', {
                botId: bot.id,
                messageId: answer.id,
                modelTokensUsed,
                newBalance
            }, 'trackTokenUsage', 'TokenTrackingTool');
            // false to continue processing the event
            return false;

        } catch (error) {
            log.error('Error tracking token usage', {
                error: error.message,
                botId: bot?.id,
                messageId: answer?.id
            }, 'trackTokenUsage', 'TokenTrackingTool');
            return false;
        }
    }

    /**
     * Check if user has enough tokens before processing or sending a message
     * @param {Object} context - Context object with bot and user info
     * @returns {Promise<boolean>} - Returns true if message should be blocked
     */
    async checkTokenBalance(context) {
        const {bot, chatId, client} = context;

        try {

            if (!bot || !bot.user_id) {
                log.warn('Bot or bot owner not found', {botId: bot?.id}, 'checkTokenBalance', 'TokenTrackingTool');
                return false;
            }

            // Get user's token balance
            const tokenBalance = await getTokenBalance(bot.user_id);

            // If no balance record exists or balance is positive, allow the message
            if (tokenBalance > 0) {
                return false;
            }

            log.info('Zero token balance detected', {
                botId: bot.id,
                chatId,
                userId: bot.user_id,
            }, 'checkTokenBalance', 'TokenTrackingTool');

            // If the balance is zero, block the message and notify the user
            await client.sendMessage(
                chatId,
                await l(context.user, "<b>This bot has run out of tokens.</b> Please contact the bot owner."),
                {parse_mode: 'HTML'},
                true
            );

            return true; // Block the message
        } catch (error) {
            log.error('Error checking token balance', {
                error: error.message,
                botId: bot?.id,
                chatId
            }, 'checkTokenBalance', 'TokenTrackingTool');
            return false; // Allow the message on error
        }
    }

    /**
     * Send notification to bot owner about low token balance
     * @param {Object} bot - Bot object
     * @param {number} balance - Current token balance
     * @param {Object} context - Context object
     * @returns {Promise<void>}
     */
    async sendLowBalanceNotification(bot, balance, context) {
        try {
            const {user} = context;
            // Get the bot owner's external user record
            if (!bot.externalUserId) {
                log.warn('Bot owner external user ID not found', {
                    botId: bot.id
                }, 'sendLowBalanceNotification', 'TokenTrackingTool');
                return;
            }

            // Create a client for the bot owner
            const MessagingClientFactory = (await import('../library/messaging/factory.js')).default;
            const ownerClient = MessagingClientFactory.createClientForBot(bot, false);
            await ownerClient.init();

            const message = "⚠️ " + await l(user, "<b>Low Token Balance Alert</b>") + " ⚠️\n\n" +
                await l(user, `Your bot "${bot.title}" is running low on tokens. `) +
                await lp(user, "Current balance: <b>{count}</b> tokens.", balance) + "\n\n" +
                await l(user, "Please add more tokens to continue using the bot.");

            // Send message to bot owner
            await ownerClient.sendMessage(bot.externalUserId, message, {parse_mode: 'HTML'}, true);

            log.info('Low balance notification sent', {
                botId: bot.id,
                balance
            }, 'sendLowBalanceNotification', 'TokenTrackingTool');
        } catch (error) {
            log.error('Error sending low balance notification', {
                error: error.message,
                botId: bot.id
            }, 'sendLowBalanceNotification', 'TokenTrackingTool');
        }
    }

    /**
     * Send notification to bot owner about zero token balance
     * @param {Object} bot - Bot object
     * @param {Object} context - Context object
     * @returns {Promise<void>}
     */
    async sendZeroBalanceNotification(bot, context) {
        try {
            const {user} = context;
            // Get the bot owner's external user record
            if (!bot.externalUserId) {
                log.warn('Bot owner external user ID not found', {
                    botId: bot.id
                }, 'sendZeroBalanceNotification', 'TokenTrackingTool');
                return;
            }

            // Create a client for the bot owner
            const MessagingClientFactory = (await import('../library/messaging/factory.js')).default;
            const ownerClient = MessagingClientFactory.createClientForBot(bot, false);
            await ownerClient.init();

            const message = "🚫 " + await l(user, "<b>Token Balance Depleted</b>") + " 🚫\n\n" +
                await l(user, `Your bot "${bot.title}" has run out of tokens. The bot will not be able to respond to messages until you add more tokens.`) + "\n\n" +
                await l(user, "Please add more tokens to resume bot operation.");

            // Send message to bot owner
            await ownerClient.sendMessage(bot.externalUserId, message, {parse_mode: 'HTML'}, true);

            log.info('Zero balance notification sent', {
                botId: bot.id
            }, 'sendZeroBalanceNotification', 'TokenTrackingTool');
        } catch (error) {
            log.error('Error sending zero balance notification', {
                error: error.message,
                botId: bot.id
            }, 'sendZeroBalanceNotification', 'TokenTrackingTool');
        }
    }

    /**
     * Handle token-related commands
     * @param {Object} eventData - Command data
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if command was handled
     */
    async handleCommand(eventData, context) {
        return false;
    }

    /**
     * Handle callback queries from dynamic buttons (bot details)
     * @param {Object} eventData - Callback query data
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if callback was handled
     */
    async handleCallbackQuery(eventData, context) {
        const {data} = eventData;
        const {client, chatId, isBotOwner} = context;

        // Only bot owners can use token menu
        if (!isBotOwner) {
            return false;
        }

        // Handle only dynamic bot detail buttons
        if (data && data.startsWith('token_bot_detail:')) {
            // Delete the message with the button after a delay
            if (eventData.message) {
                setTimeout(() => {
                    try {
                        client.deleteMessage(eventData.message.chat.id, eventData.message.message_id);
                    } catch (e) {
                        log.error('Failed to delete message', {
                            error: e.message,
                            chatId: eventData.message.chat.id,
                            messageId: eventData.message.message_id
                        }, 'handleCallbackQuery', 'TokenTrackingTool');
                    }
                }, 2000);
            }

            const botId = data.split(':')[1];
            return await this.showBotTokenUsage(botId, context);
        }

        return false;
    }

    /**
     * Show token balance menu with options
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if menu was shown
     */
    async showTokenBalanceMenu(context) {
        const {client, chatId, bot, user} = context;

        try {
            // Get user's token balance
            const balance = await getTokenBalance(bot.user_id);

            // Get menu items for token_menu position
            const menuItems = await client.getMenuItems('token_menu', ['owner']);

            // Get translated menu title
            const menuTitle = "💰 " + await l(user, "<b>Token Balance</b>") + "\n\n" +
                await lp(user, "Current balance: <b>{count}</b> tokens", balance) + "\n"

            // Send menu to user
            await client.sendMenu(chatId, menuTitle, menuItems);
            return true;
        } catch (error) {
            log.error('Error showing token balance menu', {
                error: error.message,
                chatId
            }, 'showTokenBalanceMenu', 'TokenTrackingTool');

            await client.sendMessage(chatId, await l(user, '<b>Error:</b> Error retrieving token balance information. Please try again later.'), {parse_mode: 'HTML'});
            return true;
        }
    }

    /**
     * Show token statistics menu with usage options
     * @param {Object} context - Context object
     * @returns {Promise<boolean>} - Returns true if menu was shown
     */
    async showTokenStatisticsMenu(context) {
        const {client, chatId, user} = context;

        try {
            // Get menu items for token_statistics_menu position
            const menuItems = await client.getMenuItems('token_statistics_menu', ['owner']);

            // Get translated menu title
            const menuTitle = "📊 " + await l(user, "<b>Token Statistics</b>") + "\n\n" +
                await l(user, "Select a time period to view token usage statistics:");

            // Send menu to user
            await client.sendMenu(chatId, menuTitle, menuItems);
            return true;
        } catch (error) {
            log.error('Error showing token statistics menu', {
                error: error.message,
                chatId
            }, 'showTokenStatisticsMenu', 'TokenTrackingTool');

            await client.sendMessage(chatId, await l(user, '<b>Error:</b> Error showing token statistics menu. Please try again later.'), {parse_mode: 'HTML'});
            return true;
        }
    }

    /**
     * Show all-time token usage statistics
     * @param {Object} context - Context object
     * @param startDate
     * @returns {Promise<boolean>} - Returns true if statistics were shown
     */
    async showTokenUsageAll(context, startDate = null) {
        const {client, chatId, bot, user} = context;

        try {
            const usageByBots = await tokenUsageByBots(bot.user_id, startDate);

            // Create inline keyboard with bot buttons
            const inlineKeyboard = [];

            // Add a button for each bot
            for (const b of usageByBots) {
                inlineKeyboard.push([
                    {
                        text: `${b.title} (` + await lp(user, '{count} tokens', b.totalTokensUsed) + ')',
                        callback_data: `token_bot_detail:${b.id}`
                    }
                ]);

            }

            // Add back button
            inlineKeyboard.push([{
                text: '⬅️ ' + await l(user, 'Back to Statistics Menu'),
                callback_data: 'token_statistics'
            }]);
            const totalTokens = usageByBots.reduce((sum, b) => sum + b.totalTokensUsed, 0);
            const totalMessages = usageByBots.reduce((sum, b) => sum + b.messageCount, 0);
            let message;
            if (!startDate) {
                message = "📊 " + await l(user, "<b>All-Time Token Usage</b>") + "\n\n";
            } else {
                message = "📅 " + await l(user, "<b>Token Usage Since:</b>") + ` <b>${startDate.toLocaleDateString()}</b>\n\n`;
            }

            message += await lp(user, "Total tokens used: <b>{count}</b>", totalTokens) + "\n" +
                await lp(user, "Total messages: <b>{count}</b>", totalMessages) + "\n" +
                await lp(user, "Average tokens per message: <b>{count}</b>", totalMessages > 0 ? Math.round(totalTokens / totalMessages) : 0) + "\n\n" +
                await l(user, "Select a bot to view detailed usage:");

            // Send message with inline keyboard
            if (inlineKeyboard.length > 1) { // > 1 because we always have the back button
                await client.sendMessage(chatId, message, {
                    parse_mode: 'HTML',
                    reply_markup: {inline_keyboard: inlineKeyboard}
                });
            } else {
                // If no bots with usage, just show a message with back button
                await client.sendMessage(chatId, message + '\n\n<i>' + await l(user, "No bots have used any tokens yet.") + '</i>', {
                    parse_mode: 'HTML',
                    reply_markup: {inline_keyboard: inlineKeyboard}
                });
            }
            return true;
        } catch (error) {
            log.error('Error showing token usage statistics', {
                error: error.message,
                chatId
            }, 'showTokenUsageAll', 'TokenTrackingTool');

            await client.sendMessage(chatId, await l(user, '<b>Error:</b> Error retrieving token usage statistics. Please try again later.'), {parse_mode: 'HTML'});
            return true;
        }
    }


    async showBotTokenUsage(botId, context) {
        const {client, chatId, user} = context;

        try {
            // Get the bot
            const botInfo = await Bot.findByPk(botId, {
                attributes: ['id', 'title']
            });

            if (!botInfo) {
                await client.sendMessage(chatId, await l(user, '<b>Error:</b> Bot not found.'), {parse_mode: 'HTML'});
                return true;
            }

            // Get all-time usage
            const allTimeUsage = await getTokenUsageByBot(botId);

            // Get first day of current month
            const firstDay = new Date();
            firstDay.setDate(1);
            firstDay.setHours(0, 0, 0, 0);

            // Get usage for current month
            const monthUsage = await getTokenUsageByBot(botId, firstDay);

            // Get date 7 days ago
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            sevenDaysAgo.setHours(0, 0, 0, 0);

            // Get usage for last 7 days
            const weekUsage = await getTokenUsageByBot(botId, sevenDaysAgo);

            // Get daily usage for the last 7 days
            const dailyUsage = await BotTokenUsage.findAll({
                attributes: [
                    [sequelize.fn('DATE', sequelize.col('usageDate')), 'date'],
                    [sequelize.fn('SUM', sequelize.col('tokensUsed')), 'total'],
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                ],
                where: {
                    botId,
                    usageDate: {[Op.gte]: sevenDaysAgo}
                },
                group: [sequelize.fn('DATE', sequelize.col('usageDate'))],
                order: [[sequelize.fn('DATE', sequelize.col('usageDate')), 'ASC']]
            });

            // Get model breakdown
            const modelUsage = await BotTokenUsage.findAll({
                attributes: [
                    'model',
                    [sequelize.fn('SUM', sequelize.col('tokensUsed')), 'total'],
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                ],
                where: {botId},
                group: ['model'],
                order: [[sequelize.literal('total'), 'DESC']],
                limit: 3 // Top 3 models
            });

            // Format the data
            const allTimeTotal = allTimeUsage.reduce((sum, b) => sum + b.tokens, 0);
            const allTimeCount = allTimeUsage.reduce((sum, b) => sum + b.messages, 0);
            const monthTotal = monthUsage.reduce((sum, b) => sum + b.tokens, 0);
            const monthCount = monthUsage.reduce((sum, b) => sum + b.messages, 0);
            const weekTotal = weekUsage.reduce((sum, b) => sum + b.tokens, 0);
            const weekCount = weekUsage.reduce((sum, b) => sum + b.messages, 0);

            // Format daily usage
            let dailyUsageText = '';
            if (dailyUsage.length > 0) {
                dailyUsageText = '\n\n<b>' + await l(user, 'Daily Usage (Last 7 Days):') + '</b>\n';
                const tokensTranslation = await l(user, 'tokens');

                for (const day of dailyUsage) {
                    const date = new Date(day.getDataValue('date'));
                    const formattedDate = date.toLocaleDateString('en-US', {month: 'short', day: 'numeric'});
                    const total = parseInt(day.getDataValue('total') || 0);
                    dailyUsageText += `${formattedDate}: <b>${total.toLocaleString()}</b> ${tokensTranslation}\n`;
                }
            }

            // Format model usage
            let modelUsageText = '';
            if (modelUsage.length > 0) {
                modelUsageText = '\n\n<b>' + await l(user, 'Model Usage:') + '</b>\n';
                const tokensTranslation = await l(user, 'tokens');

                for (const model of modelUsage) {
                    const modelName = model.model || 'Unknown';
                    const total = parseInt(model.getDataValue('total') || 0);
                    const percentage = allTimeTotal > 0 ? Math.round((total / allTimeTotal) * 100) : 0;
                    modelUsageText += `${modelName}: <b>${total.toLocaleString()}</b> ${tokensTranslation} (${percentage}%)\n`;
                }
            }

            // Create back buttons
            const backButtons = {
                inline_keyboard: [
                    [{text: '⬅️ ' + await l(user, 'Back to All-Time Usage'), callback_data: 'token_usage_all'}],
                    [{text: '⬅️ ' + await l(user, 'Back to Month Usage'), callback_data: 'token_usage_month'}],
                    [{text: '⬅️ ' + await l(user, 'Back to Week Usage'), callback_data: 'token_usage_week'}],
                    [{text: '⬅️ ' + await l(user, 'Back to Statistics Menu'), callback_data: 'token_statistics'}],
                    [{text: '⬅️ ' + await l(user, 'Back to Token Menu'), callback_data: 'token_balance'}]
                ]
            };

            const monthName = firstDay.toLocaleString('default', {month: 'long'});

            const message = `📊 ` + await l(user, `<b>Token Usage for ${botInfo.title}</b>`) + "\n\n" +
                await l(user, "<b>All-Time Usage:</b>") + "\n" +
                await lp(user, "Total tokens: {count}", allTimeTotal) + "\n" +
                await lp(user, "Total messages: {count}", allTimeCount) + "\n" +
                await lp(user, "Average per message: {count}", allTimeCount > 0 ? Math.round(allTimeTotal / allTimeCount) : 0) + "\n\n" +
                await l(user, `<b>${monthName} Usage:</b>`) + "\n" +
                await lp(user, "Total tokens: {count}", monthTotal) + "\n" +
                await lp(user, "Total messages: {count}", monthCount) + "\n\n" +
                await l(user, "<b>Last 7 Days:</b>") + "\n" +
                await lp(user, "Total tokens: {count}", weekTotal) + "\n" +
                await lp(user, "Total messages: {count}", weekCount) +
                dailyUsageText +
                modelUsageText;

            // For bot details, we use custom back buttons instead of menu
            await client.sendMessage(chatId, message, {parse_mode: 'HTML', reply_markup: backButtons});
            return true;
        } catch (error) {
            log.error('Error showing bot token usage', {
                error: error.message,
                botId,
                chatId
            }, 'showBotTokenUsage', 'TokenTrackingTool');

            await client.sendMessage(chatId, await l(user, '<b>Error:</b> Error retrieving bot token usage statistics. Please try again later.'), {parse_mode: 'HTML'});
            return true;
        }
    }
}

export default TokenTrackingTool;
