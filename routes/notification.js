import express from 'express';
import {MlmIdToUserId, updateTokenBalance} from "../utils/index.js";
import {Bot, ExternalUser} from "../models/associations.js";
import {log} from "../library/log.js";
import {lp} from "../library/translation.js";
import MessagingClientFactory from "../library/messaging/factory.js";
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

router.post('/payment', async (req, res) => {
    //todo lock for 5 minutes
    if (req.headers.authorization !== `Bearer ${process.env.MLM_API_KEY}`) {
        return res.status(401).json({message: 'Unauthorized'});
    }
    log.info('Payment notification received', {
        body: req.body
    }, 'payment', 'notification.js');
    const {chatId, status, link, price, provider, business, packet} = req.body;
    if (business === 4) {
        //todo check statuses
        //add 1m tokens
        const userId = await MlmIdToUserId(chatId);
        const amount = 1000000;

        const newBalance = await updateTokenBalance(userId, amount);

        try {
            const externalUser = await ExternalUser.findOne({
                where: {externalId: chatId, botId: process.env.CONSTRUCTOR_BOT_ID}
            });
            let msg = await lp(externalUser, 'Your token balance has been updated. New balance: {count} tokens.', newBalance);
            //create telegram instance for CONSTRUCTOR_BOT_ID
            //send message to user
            const bot = await Bot.findByPk(process.env.CONSTRUCTOR_BOT_ID);
            const telegramClient = await MessagingClientFactory.createClientForBot(bot, false);
            await telegramClient.sendMessage(chatId, msg, {parse_mode: 'HTML'}, true);
        } catch (e) {
            log.error('Error sending message', {
                error: e.message,
                stack: e.stack
            }, 'handleTokenBalanceUpdate', 'TokenTrackingTool');
        }

        return res.status(200).json({
            success: true,
            balance: newBalance
        });
    }
    res.json({message: 'Notification received'});
});

export default router;